"""
SQL Extractor Utility

Utility class for extracting validation rules and schema information from SQL statements
using SQLGlot AST parsing.
"""

from typing import Dict, <PERSON>, <PERSON><PERSON>, List
import sqlglot
from sqlglot import expressions as exp


class SQLExtractor:
    """Utility class for extracting rules and schema from SQL AST"""
    
    def __init__(self):
        self.data_type_mapping = {
            "VARCHAR": "string",
            "CHAR": "string", 
            "TEXT": "string",
            "INTEGER": "integer",
            "INT": "integer",
            "BIGINT": "integer",
            "DECIMAL": "decimal",
            "NUMERIC": "decimal",
            "FLOAT": "float",
            "DOUBLE": "float",
            "BOOLEAN": "boolean",
            "DATE": "date",
            "TIMESTAMP": "timestamp",
            "TIME": "time"
        }
    
    def extract_from_statement(self, statement) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Extract rules and schema from a SQL statement"""
        rules = {}
        schema = {}
        
        try:
            if isinstance(statement, exp.Create):
                # Handle CREATE TABLE statements
                table_rules, table_schema = self._extract_from_create_table(statement)
                rules.update(table_rules)
                schema.update(table_schema)
                
            elif isinstance(statement, exp.Alter):
                # Handle ALTER TABLE statements
                alter_rules, alter_schema = self._extract_from_alter_table(statement)
                rules.update(alter_rules)
                schema.update(alter_schema)
                
            elif isinstance(statement, exp.Select):
                # Handle SELECT statements with validation logic
                select_rules = self._extract_from_select(statement)
                rules.update(select_rules)
                
        except Exception as e:
            print(f"Warning: Failed to extract from statement: {str(e)}")
        
        return rules, schema
    
    def _extract_from_create_table(self, create_stmt) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Extract rules and schema from CREATE TABLE statement"""
        rules = {}
        schema = {}
        
        try:
            # Get table name
            table_name = create_stmt.this.name if create_stmt.this else "unknown_table"
            
            # Extract column definitions
            if hasattr(create_stmt, 'expressions') and create_stmt.expressions:
                for expr in create_stmt.expressions:
                    if isinstance(expr, exp.ColumnDef):
                        column_name = expr.this.name
                        
                        # Extract data type
                        data_type = self._extract_data_type(expr.kind)
                        
                        # Extract constraints
                        constraints = self._extract_column_constraints(expr)
                        
                        # Build rule
                        rules[column_name] = {
                            "column_name": column_name,
                            "data_type": data_type,
                            "required": constraints.get("not_null", False),
                            "constraints": constraints,
                            "business_rules": []
                        }
                        
                        # Build schema
                        schema[column_name] = {
                            "column_name": column_name,
                            "data_type": data_type,
                            "nullable": not constraints.get("not_null", False),
                            "description": f"Column from table {table_name}",
                            "constraints": constraints
                        }
                        
        except Exception as e:
            print(f"Warning: Failed to extract from CREATE TABLE: {str(e)}")
        
        return rules, schema
    
    def _extract_from_alter_table(self, alter_stmt) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Extract rules and schema from ALTER TABLE statement"""
        rules = {}
        schema = {}
        
        try:
            # Handle ADD CONSTRAINT statements
            if hasattr(alter_stmt, 'expressions'):
                for expr in alter_stmt.expressions:
                    if isinstance(expr, exp.AlterColumn):
                        # Handle column alterations
                        column_rules = self._extract_alter_column_rules(expr)
                        rules.update(column_rules)
                        
        except Exception as e:
            print(f"Warning: Failed to extract from ALTER TABLE: {str(e)}")
        
        return rules, schema
    
    def _extract_from_select(self, select_stmt) -> Dict[str, Any]:
        """Extract validation rules from SELECT statements with business logic"""
        rules = {}
        
        try:
            # Look for WHERE clauses with validation logic
            if hasattr(select_stmt, 'where') and select_stmt.where:
                where_rules = self._extract_where_clause_rules(select_stmt.where)
                rules.update(where_rules)
                
            # Look for CASE statements with validation logic
            if hasattr(select_stmt, 'expressions'):
                for expr in select_stmt.expressions:
                    case_rules = self._extract_case_rules(expr)
                    rules.update(case_rules)
                    
        except Exception as e:
            print(f"Warning: Failed to extract from SELECT: {str(e)}")
        
        return rules
    
    def _extract_data_type(self, kind_expr) -> str:
        """Extract and normalize data type"""
        if not kind_expr:
            return "string"
            
        try:
            data_type = str(kind_expr).upper()
            
            # Map to standard types
            for sql_type, standard_type in self.data_type_mapping.items():
                if sql_type in data_type:
                    return standard_type
                    
            return "string"  # Default fallback
            
        except:
            return "string"
    
    def _extract_column_constraints(self, column_def) -> Dict[str, Any]:
        """Extract constraints from column definition"""
        constraints = {}
        
        try:
            if hasattr(column_def, 'constraints') and column_def.constraints:
                for constraint in column_def.constraints:
                    if isinstance(constraint, exp.NotNullColumnConstraint):
                        constraints["not_null"] = True
                    elif isinstance(constraint, exp.UniqueColumnConstraint):
                        constraints["unique"] = True
                    elif isinstance(constraint, exp.PrimaryKeyColumnConstraint):
                        constraints["primary_key"] = True
                    elif isinstance(constraint, exp.CheckColumnConstraint):
                        constraints["check"] = str(constraint.this)
                        
        except Exception as e:
            print(f"Warning: Failed to extract constraints: {str(e)}")
        
        return constraints
    
    def _extract_alter_column_rules(self, alter_expr) -> Dict[str, Any]:
        """Extract rules from ALTER COLUMN expressions"""
        rules = {}
        
        try:
            if hasattr(alter_expr, 'this') and alter_expr.this:
                column_name = alter_expr.this.name
                
                # Extract constraint modifications
                if hasattr(alter_expr, 'kind'):
                    constraint_type = str(alter_expr.kind).upper()
                    
                    rules[column_name] = {
                        "column_name": column_name,
                        "data_type": "string",  # Default
                        "required": "NOT NULL" in constraint_type,
                        "constraints": {"alter_constraint": constraint_type},
                        "business_rules": []
                    }
                    
        except Exception as e:
            print(f"Warning: Failed to extract ALTER COLUMN rules: {str(e)}")
        
        return rules
    
    def _extract_where_clause_rules(self, where_expr) -> Dict[str, Any]:
        """Extract validation rules from WHERE clauses"""
        rules = {}
        
        try:
            # Look for column comparisons and constraints
            if isinstance(where_expr, exp.Binary):
                left = str(where_expr.left) if where_expr.left else ""
                right = str(where_expr.right) if where_expr.right else ""
                operator = str(where_expr.key) if hasattr(where_expr, 'key') else ""
                
                # Extract column-based rules
                if left and not left.isdigit():  # Likely a column name
                    rules[left] = {
                        "column_name": left,
                        "data_type": "string",
                        "required": True,
                        "constraints": {
                            "where_condition": f"{left} {operator} {right}"
                        },
                        "business_rules": [f"Must satisfy: {left} {operator} {right}"]
                    }
                    
        except Exception as e:
            print(f"Warning: Failed to extract WHERE clause rules: {str(e)}")
        
        return rules
    
    def _extract_case_rules(self, expr) -> Dict[str, Any]:
        """Extract validation rules from CASE expressions"""
        rules = {}
        
        try:
            if isinstance(expr, exp.Case):
                # Extract CASE logic as business rules
                case_logic = str(expr)
                
                # Try to identify the column being evaluated
                if hasattr(expr, 'this') and expr.this:
                    column_name = str(expr.this)
                    
                    rules[column_name] = {
                        "column_name": column_name,
                        "data_type": "string",
                        "required": True,
                        "constraints": {},
                        "business_rules": [f"CASE logic: {case_logic}"]
                    }
                    
        except Exception as e:
            print(f"Warning: Failed to extract CASE rules: {str(e)}")
        
        return rules
