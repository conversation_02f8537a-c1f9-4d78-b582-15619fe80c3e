"""
CBRE Validation Agent - Modular AI Agents Package

This package contains four specialized AI agents for the validation pipeline:
- SP_Parser_Agent: SQL stored procedure parsing and rule extraction
- FileValidationAgent: File validation against rules and schema
- DataValidationAgent: Backend data validation with business logic
- GuardrailAgent: AI output safety and hallucination prevention
"""

from .sp_parser.sp_parser_agent import SPParserAgent
from .file_validator.file_validation_agent import FileValidationAgent
from .data_validator.data_validation_agent import DataValidationAgent
from .guardrail.guardrail_agent import GuardrailAgent

__all__ = [
    'SPParserAgent',
    'FileValidationAgent', 
    'DataValidationAgent',
    'GuardrailAgent'
]

__version__ = "1.0.0"
