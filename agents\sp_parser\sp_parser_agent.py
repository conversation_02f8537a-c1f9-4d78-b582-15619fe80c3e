"""
SP_Parser_Agent - AI Extraction Agent

Parses SQL stored procedures to extract validation rules and schema definitions.
Uses SQLGlot for structured parsing with AI-based interpretation fallback.

Outputs:
- rules.json: Column-level validation constraints
- column_template.json: Schema definition
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Tu<PERSON>, Optional
from pathlib import Path

import sqlglot
from pydantic import BaseModel, Field

from core.agent_base import AgentBase
from .sql_extractor import SQLExtractor


class ValidationRule(BaseModel):
    """Pydantic model for validation rules"""
    column_name: str
    data_type: str
    required: bool = True
    constraints: Dict[str, Any] = Field(default_factory=dict)
    business_rules: list = Field(default_factory=list)


class ColumnTemplate(BaseModel):
    """Pydantic model for column template"""
    column_name: str
    data_type: str
    nullable: bool = False
    description: Optional[str] = None
    constraints: Dict[str, Any] = Field(default_factory=dict)


class SPParserAgent(AgentBase):
    """
    AI Extraction Agent for SQL Stored Procedure Parsing
    
    Responsibilities:
    1. Parse SQL stored procedures using SQLGlot
    2. Extract validation rules and schema information
    3. Generate rules.json and column_template.json
    4. Provide AI-based interpretation fallback
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            agent_name="SP_Parser_Agent",
            agent_type="AI Extraction Agent",
            agent_role="SQL stored procedure parsing and rule extraction specialist"
        )
        
        self.config = config or {}
        self.sql_extractor = SQLExtractor()
        self.output_dir = Path(self.config.get("output_dir", "data/output"))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def parse_sql_file(self, sql_file_path: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Parse SQL file and extract validation rules and schema
        
        Args:
            sql_file_path: Path to SQL stored procedure file
            
        Returns:
            Tuple of (rules_dict, column_template_dict)
        """
        try:
            self.log_info(f"Starting SQL file parsing: {sql_file_path}")
            
            # Read SQL file
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            # Try SQLGlot parsing first
            rules, schema = self._parse_with_sqlglot(sql_content)
            
            if not rules and not schema:
                # Fallback to AI-based interpretation
                self.log_warning("SQLGlot parsing failed, falling back to AI interpretation")
                rules, schema = self._parse_with_ai(sql_content)
            
            # Validate and save outputs
            rules_output = self._save_rules_json(rules)
            schema_output = self._save_column_template_json(schema)
            
            self.log_success(f"Successfully parsed SQL file. Generated {len(rules)} rules and {len(schema)} columns")
            
            return rules_output, schema_output
            
        except Exception as e:
            self.log_error(f"Failed to parse SQL file: {str(e)}")
            raise
    
    def _parse_with_sqlglot(self, sql_content: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Parse SQL using SQLGlot library"""
        try:
            # Parse SQL statements
            statements = sqlglot.parse(sql_content, dialect="snowflake")
            
            rules = {}
            schema = {}
            
            for statement in statements:
                if statement:
                    # Extract table definitions and constraints
                    extracted_rules, extracted_schema = self.sql_extractor.extract_from_statement(statement)
                    rules.update(extracted_rules)
                    schema.update(extracted_schema)
            
            return rules, schema
            
        except Exception as e:
            self.log_warning(f"SQLGlot parsing failed: {str(e)}")
            return {}, {}
    
    def _parse_with_ai(self, sql_content: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """AI-based SQL interpretation fallback"""
        system_prompt = """
        You are an expert SQL analyst specializing in extracting validation rules and schema definitions 
        from stored procedures. Analyze the provided SQL code and extract:
        
        1. Column-level validation constraints (data types, nullability, ranges, patterns)
        2. Business rules and logic constraints
        3. Table schema definitions
        4. Referential integrity rules
        
        Return structured JSON with 'rules' and 'schema' sections.
        """
        
        user_prompt = f"""
        Analyze this SQL stored procedure and extract validation rules and schema:
        
        ```sql
        {sql_content}
        ```
        
        Extract:
        1. All column definitions with data types and constraints
        2. Validation rules (CHECK constraints, business logic)
        3. Required vs optional fields
        4. Data format requirements
        
        Format as JSON with 'rules' and 'schema' keys.
        """
        
        try:
            response = self.analyze_with_ai(system_prompt, user_prompt)
            
            if response.get("success"):
                # Parse AI response to extract rules and schema
                ai_output = response.get("structured_response", {})
                rules = ai_output.get("rules", {})
                schema = ai_output.get("schema", {})
                
                return rules, schema
            else:
                self.log_error("AI-based SQL parsing failed")
                return {}, {}
                
        except Exception as e:
            self.log_error(f"AI parsing failed: {str(e)}")
            return {}, {}
    
    def _save_rules_json(self, rules: Dict[str, Any]) -> Dict[str, Any]:
        """Save validation rules to rules.json"""
        try:
            # Validate rules using Pydantic
            validated_rules = {}
            for column, rule_data in rules.items():
                try:
                    validated_rule = ValidationRule(**rule_data)
                    validated_rules[column] = validated_rule.dict()
                except Exception as e:
                    self.log_warning(f"Invalid rule for column {column}: {str(e)}")
                    continue
            
            # Save to file
            rules_file = self.output_dir / "rules.json"
            with open(rules_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "metadata": {
                        "generated_at": datetime.now().isoformat(),
                        "agent": self.agent_name,
                        "version": "1.0.0"
                    },
                    "rules": validated_rules
                }, f, indent=2)
            
            self.log_info(f"Saved rules.json with {len(validated_rules)} rules")
            return validated_rules
            
        except Exception as e:
            self.log_error(f"Failed to save rules.json: {str(e)}")
            return {}
    
    def _save_column_template_json(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Save column template to column_template.json"""
        try:
            # Validate schema using Pydantic
            validated_schema = {}
            for column, schema_data in schema.items():
                try:
                    validated_column = ColumnTemplate(**schema_data)
                    validated_schema[column] = validated_column.dict()
                except Exception as e:
                    self.log_warning(f"Invalid schema for column {column}: {str(e)}")
                    continue
            
            # Save to file
            template_file = self.output_dir / "column_template.json"
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "metadata": {
                        "generated_at": datetime.now().isoformat(),
                        "agent": self.agent_name,
                        "version": "1.0.0"
                    },
                    "schema": validated_schema
                }, f, indent=2)
            
            self.log_info(f"Saved column_template.json with {len(validated_schema)} columns")
            return validated_schema
            
        except Exception as e:
            self.log_error(f"Failed to save column_template.json: {str(e)}")
            return {}
    
    def validate_sql_syntax(self, sql_content: str) -> bool:
        """Validate SQL syntax using SQLGlot"""
        try:
            statements = sqlglot.parse(sql_content, dialect="snowflake")
            return len(statements) > 0 and all(stmt is not None for stmt in statements)
        except:
            return False
    
    def get_supported_dialects(self) -> list:
        """Get list of supported SQL dialects"""
        return ["snowflake", "postgres", "mysql", "sqlite", "bigquery", "redshift"]
