"""
Agent Base Class for CBRE Validation System

Provides foundation for all AI-powered validation agents with standardized
logging, AI interaction, and microservice-ready architecture.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
from abc import ABC, abstractmethod

from pydantic import BaseModel, Field


class AgentMetadata(BaseModel):
    """Metadata for agent operations"""
    agent_name: str
    agent_type: str
    agent_role: str
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    version: str = "1.0.0"


class AgentResponse(BaseModel):
    """Standardized agent response format"""
    success: bool
    agent_name: str
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    data: Optional[Dict[str, Any]] = None
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    metadata: Optional[Dict[str, Any]] = None


class AgentBase(ABC):
    """
    Base class for all AI agents in the CBRE validation system
    
    Provides:
    - Standardized logging with agent-specific context
    - AI interaction capabilities via LangChain/OpenAI
    - Microservice-ready architecture
    - Error handling and response formatting
    - Configuration management
    """
    
    def __init__(self, agent_name: str, agent_type: str, agent_role: str, config: Optional[Dict[str, Any]] = None):
        self.agent_name = agent_name
        self.agent_type = agent_type
        self.agent_role = agent_role
        self.config = config or {}
        
        # Initialize logging
        self._setup_logging()
        
        # Initialize AI capabilities
        self._setup_ai()
        
        # Initialize metadata
        self.metadata = AgentMetadata(
            agent_name=agent_name,
            agent_type=agent_type,
            agent_role=agent_role
        )
        
        # Track operations
        self.operation_history: List[Dict[str, Any]] = []
        
    def _setup_logging(self):
        """Setup agent-specific logging"""
        # Create logs directory
        log_dir = Path("data/logs")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logger
        self.logger = logging.getLogger(self.agent_name)
        self.logger.setLevel(logging.INFO)
        
        # Create file handler
        log_file = log_dir / f"{self.agent_name.lower().replace(' ', '_')}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        if not self.logger.handlers:
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def _setup_ai(self):
        """Setup AI capabilities"""
        try:
            from core.llm_config import get_llm
            self.llm = get_llm()
            self.ai_available = True
            self.log_info("AI capabilities initialized successfully")
        except Exception as e:
            self.llm = None
            self.ai_available = False
            self.log_warning(f"AI capabilities not available: {str(e)}")
    
    def log_info(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """Log info message with agent context"""
        self.logger.info(f"[{self.agent_name}] {message}")
        self._record_operation("INFO", message, extra_data)
    
    def log_warning(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """Log warning message with agent context"""
        self.logger.warning(f"[{self.agent_name}] {message}")
        self._record_operation("WARNING", message, extra_data)
    
    def log_error(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """Log error message with agent context"""
        self.logger.error(f"[{self.agent_name}] {message}")
        self._record_operation("ERROR", message, extra_data)
    
    def log_success(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """Log success message with agent context"""
        self.logger.info(f"[{self.agent_name}] SUCCESS: {message}")
        self._record_operation("SUCCESS", message, extra_data)
    
    def _record_operation(self, level: str, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """Record operation in history"""
        operation = {
            "timestamp": datetime.now().isoformat(),
            "level": level,
            "message": message,
            "agent": self.agent_name,
            "extra_data": extra_data or {}
        }
        self.operation_history.append(operation)
    
    def analyze_with_ai(self, system_prompt: str, user_prompt: str, context_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Core AI analysis method with standardized error handling
        
        Args:
            system_prompt: System instruction for the AI
            user_prompt: User query/request
            context_data: Additional context data
            
        Returns:
            Standardized response dictionary
        """
        try:
            if not self.ai_available or self.llm is None:
                return {
                    "success": False,
                    "error": "AI capabilities not available",
                    "raw_response": "AI analysis unavailable. Check OpenAI API configuration.",
                    "structured_response": {
                        "analysis": "AI dependencies not available",
                        "recommendation": "Configure OpenAI API key and install dependencies"
                    },
                    "agent": self.agent_name,
                    "timestamp": datetime.now().isoformat()
                }
            
            # Prepare messages
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Add context if provided
            if context_data:
                context_message = f"Additional context: {json.dumps(context_data, indent=2)}"
                messages.append({"role": "user", "content": context_message})
            
            # Call LLM
            response = self.llm.invoke(messages)
            raw_response = response.content if hasattr(response, 'content') else str(response)
            
            # Try to parse structured response
            structured_response = self._parse_ai_response(raw_response)
            
            result = {
                "success": True,
                "raw_response": raw_response,
                "structured_response": structured_response,
                "agent": self.agent_name,
                "timestamp": datetime.now().isoformat()
            }
            
            self.log_info("AI analysis completed successfully")
            return result
            
        except Exception as e:
            error_msg = f"AI analysis failed: {str(e)}"
            self.log_error(error_msg)
            
            return {
                "success": False,
                "error": error_msg,
                "raw_response": "",
                "structured_response": {},
                "agent": self.agent_name,
                "timestamp": datetime.now().isoformat()
            }
    
    def _parse_ai_response(self, raw_response: str) -> Dict[str, Any]:
        """Parse AI response to extract structured data"""
        try:
            # Try to find JSON in the response
            import re
            json_match = re.search(r'\{.*\}', raw_response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            # If no JSON found, create structured response
            return {
                "analysis": raw_response,
                "recommendation": "Review the analysis and take appropriate action"
            }
            
        except:
            return {
                "analysis": raw_response,
                "recommendation": "Could not parse structured response"
            }
    
    def create_response(self, success: bool, data: Optional[Dict[str, Any]] = None, 
                       errors: Optional[List[str]] = None, warnings: Optional[List[str]] = None,
                       metadata: Optional[Dict[str, Any]] = None) -> AgentResponse:
        """Create standardized agent response"""
        return AgentResponse(
            success=success,
            agent_name=self.agent_name,
            data=data,
            errors=errors or [],
            warnings=warnings or [],
            metadata=metadata
        )
    
    def get_operation_summary(self) -> Dict[str, Any]:
        """Get summary of all operations performed by this agent"""
        total_ops = len(self.operation_history)
        errors = len([op for op in self.operation_history if op["level"] == "ERROR"])
        warnings = len([op for op in self.operation_history if op["level"] == "WARNING"])
        successes = len([op for op in self.operation_history if op["level"] == "SUCCESS"])
        
        return {
            "agent_name": self.agent_name,
            "agent_type": self.agent_type,
            "total_operations": total_ops,
            "errors": errors,
            "warnings": warnings,
            "successes": successes,
            "last_operation": self.operation_history[-1] if self.operation_history else None,
            "metadata": self.metadata.dict()
        }
    
    def save_operation_log(self, output_path: Optional[str] = None) -> str:
        """Save complete operation log to file"""
        if not output_path:
            log_dir = Path("data/logs")
            log_dir.mkdir(parents=True, exist_ok=True)
            output_path = log_dir / f"{self.agent_name.lower().replace(' ', '_')}_operations.json"
        
        log_data = {
            "agent_metadata": self.metadata.dict(),
            "operation_summary": self.get_operation_summary(),
            "operation_history": self.operation_history,
            "generated_at": datetime.now().isoformat()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2)
        
        self.log_info(f"Operation log saved to {output_path}")
        return str(output_path)
    
    @abstractmethod
    def run(self, *args, **kwargs) -> AgentResponse:
        """
        Main execution method for the agent
        Must be implemented by each specific agent
        """
        pass
