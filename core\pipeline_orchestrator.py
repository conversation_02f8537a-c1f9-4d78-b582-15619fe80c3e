"""
Pipeline Orchestrator

Main orchestration component that manages the end-to-end agent flow:
SP_Parser_Agent → FileValidationAgent → DataValidationAgent → GuardrailAgent
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from .agent_base import AgentBase, AgentResponse
from agents.sp_parser import SPParserAgent
from agents.file_validator import FileValidationAgent


class PipelineOrchestrator(AgentBase):
    """
    Main Pipeline Orchestrator
    
    Manages the complete validation pipeline with standardized input/output formats
    between agents and comprehensive error handling.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            agent_name="PipelineOrchestrator",
            agent_type="Pipeline Manager",
            agent_role="End-to-end validation pipeline orchestration"
        )
        
        self.config = config or {}
        self.output_dir = Path(self.config.get("output_dir", "data/output"))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize agents
        self._initialize_agents()
        
        # Track pipeline state
        self.pipeline_state = {
            "current_stage": None,
            "completed_stages": [],
            "failed_stages": [],
            "stage_outputs": {}
        }
    
    def _initialize_agents(self):
        """Initialize all pipeline agents"""
        try:
            self.sp_parser = SPParserAgent(self.config.get("sp_parser", {}))
            self.file_validator = FileValidationAgent(self.config.get("file_validator", {}))
            # TODO: Initialize DataValidationAgent and GuardrailAgent when created
            
            self.log_info("All pipeline agents initialized successfully")
            
        except Exception as e:
            self.log_error(f"Failed to initialize agents: {str(e)}")
            raise
    
    def run(self, sql_file: str, data_file: str) -> AgentResponse:
        """
        Run the complete validation pipeline
        
        Args:
            sql_file: Path to SQL stored procedure file
            data_file: Path to Excel data file to validate
            
        Returns:
            AgentResponse with complete pipeline results
        """
        try:
            self.log_info(f"Starting pipeline execution: SQL={sql_file}, Data={data_file}")
            
            # Stage 1: SP_Parser_Agent
            stage1_result = self._run_sp_parser_stage(sql_file)
            if not stage1_result.success:
                return self._handle_stage_failure("sp_parser", stage1_result)
            
            # Stage 2: FileValidationAgent
            stage2_result = self._run_file_validation_stage(
                data_file, 
                stage1_result.data["rules_file"],
                stage1_result.data["schema_file"]
            )
            if not stage2_result.success:
                return self._handle_stage_failure("file_validation", stage2_result)
            
            # Stage 3: DataValidationAgent (TODO: Implement when created)
            # stage3_result = self._run_data_validation_stage(stage2_result.data["output_file"])
            
            # Stage 4: GuardrailAgent (TODO: Implement when created)
            # stage4_result = self._run_guardrail_stage(stage3_result.data)
            
            # Compile final results
            final_result = self._compile_pipeline_results([stage1_result, stage2_result])
            
            self.log_success("Pipeline execution completed successfully")
            return final_result
            
        except Exception as e:
            error_msg = f"Pipeline execution failed: {str(e)}"
            self.log_error(error_msg)
            return self.create_response(
                success=False,
                errors=[error_msg],
                metadata={"pipeline_state": self.pipeline_state}
            )
    
    def _run_sp_parser_stage(self, sql_file: str) -> AgentResponse:
        """Run SP_Parser_Agent stage"""
        try:
            self.pipeline_state["current_stage"] = "sp_parser"
            self.log_info("Starting SP_Parser_Agent stage")
            
            # Parse SQL file
            rules, schema = self.sp_parser.parse_sql_file(sql_file)
            
            # Save outputs
            rules_file = self.output_dir / "rules.json"
            schema_file = self.output_dir / "column_template.json"
            
            stage_result = self.create_response(
                success=True,
                data={
                    "rules": rules,
                    "schema": schema,
                    "rules_file": str(rules_file),
                    "schema_file": str(schema_file),
                    "input_sql_file": sql_file
                },
                metadata={"stage": "sp_parser", "agent": self.sp_parser.agent_name}
            )
            
            self.pipeline_state["completed_stages"].append("sp_parser")
            self.pipeline_state["stage_outputs"]["sp_parser"] = stage_result.data
            
            self.log_success("SP_Parser_Agent stage completed successfully")
            return stage_result
            
        except Exception as e:
            error_msg = f"SP_Parser_Agent stage failed: {str(e)}"
            self.log_error(error_msg)
            self.pipeline_state["failed_stages"].append("sp_parser")
            
            return self.create_response(
                success=False,
                errors=[error_msg],
                metadata={"stage": "sp_parser", "error": str(e)}
            )
    
    def _run_file_validation_stage(self, data_file: str, rules_file: str, schema_file: str) -> AgentResponse:
        """Run FileValidationAgent stage"""
        try:
            self.pipeline_state["current_stage"] = "file_validation"
            self.log_info("Starting FileValidationAgent stage")
            
            # Validate file
            validation_result = self.file_validator.run(data_file, rules_file, schema_file)
            
            self.pipeline_state["completed_stages"].append("file_validation")
            self.pipeline_state["stage_outputs"]["file_validation"] = validation_result.data
            
            if validation_result.success:
                self.log_success("FileValidationAgent stage completed successfully")
            else:
                self.log_warning("FileValidationAgent stage completed with issues")
            
            return validation_result
            
        except Exception as e:
            error_msg = f"FileValidationAgent stage failed: {str(e)}"
            self.log_error(error_msg)
            self.pipeline_state["failed_stages"].append("file_validation")
            
            return self.create_response(
                success=False,
                errors=[error_msg],
                metadata={"stage": "file_validation", "error": str(e)}
            )
    
    def _handle_stage_failure(self, stage_name: str, failed_result: AgentResponse) -> AgentResponse:
        """Handle stage failure and return appropriate response"""
        self.pipeline_state["failed_stages"].append(stage_name)
        self.pipeline_state["current_stage"] = None
        
        error_msg = f"Pipeline failed at {stage_name} stage"
        self.log_error(error_msg)
        
        return self.create_response(
            success=False,
            errors=[error_msg] + failed_result.errors,
            warnings=failed_result.warnings,
            metadata={
                "failed_stage": stage_name,
                "pipeline_state": self.pipeline_state,
                "stage_result": failed_result.dict()
            }
        )
    
    def _compile_pipeline_results(self, stage_results: List[AgentResponse]) -> AgentResponse:
        """Compile results from all pipeline stages"""
        try:
            # Aggregate data from all stages
            compiled_data = {
                "pipeline_summary": {
                    "total_stages": len(stage_results),
                    "successful_stages": len([r for r in stage_results if r.success]),
                    "failed_stages": len([r for r in stage_results if not r.success]),
                    "completed_stages": self.pipeline_state["completed_stages"],
                    "failed_stages": self.pipeline_state["failed_stages"]
                },
                "stage_outputs": self.pipeline_state["stage_outputs"],
                "final_outputs": self._identify_final_outputs()
            }
            
            # Aggregate errors and warnings
            all_errors = []
            all_warnings = []
            
            for result in stage_results:
                all_errors.extend(result.errors)
                all_warnings.extend(result.warnings)
            
            # Determine overall success
            overall_success = all(result.success for result in stage_results)
            
            return self.create_response(
                success=overall_success,
                data=compiled_data,
                errors=all_errors,
                warnings=all_warnings,
                metadata={
                    "pipeline_completed_at": datetime.now().isoformat(),
                    "pipeline_state": self.pipeline_state
                }
            )
            
        except Exception as e:
            error_msg = f"Failed to compile pipeline results: {str(e)}"
            self.log_error(error_msg)
            
            return self.create_response(
                success=False,
                errors=[error_msg],
                metadata={"pipeline_state": self.pipeline_state}
            )
    
    def _identify_final_outputs(self) -> Dict[str, Any]:
        """Identify the final output files from the pipeline"""
        final_outputs = {}
        
        try:
            # Get outputs from each completed stage
            for stage, outputs in self.pipeline_state["stage_outputs"].items():
                if stage == "sp_parser":
                    final_outputs["rules_json"] = outputs.get("rules_file")
                    final_outputs["column_template_json"] = outputs.get("schema_file")
                elif stage == "file_validation":
                    final_outputs["validated_output_xlsx"] = outputs.get("output_file")
                elif stage == "data_validation":
                    final_outputs["validation_log_json"] = outputs.get("log_file")
                elif stage == "guardrail":
                    final_outputs["guardrail_log_json"] = outputs.get("log_file")
            
        except Exception as e:
            self.log_warning(f"Failed to identify final outputs: {str(e)}")
        
        return final_outputs
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status"""
        return {
            "pipeline_state": self.pipeline_state,
            "agent_summaries": {
                "sp_parser": self.sp_parser.get_operation_summary() if hasattr(self, 'sp_parser') else None,
                "file_validator": self.file_validator.get_operation_summary() if hasattr(self, 'file_validator') else None
            },
            "orchestrator_summary": self.get_operation_summary()
        }
    
    def save_pipeline_report(self, output_path: Optional[str] = None) -> str:
        """Save comprehensive pipeline report"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.output_dir / f"pipeline_report_{timestamp}.json"
        
        report_data = {
            "pipeline_metadata": {
                "orchestrator": self.agent_name,
                "generated_at": datetime.now().isoformat(),
                "version": "1.0.0"
            },
            "pipeline_status": self.get_pipeline_status(),
            "configuration": self.config
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2)
        
        self.log_info(f"Pipeline report saved to {output_path}")
        return str(output_path)
