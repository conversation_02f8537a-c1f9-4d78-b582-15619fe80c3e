# CBRE Validation Agent

A comprehensive automated validation system for NAV (Net Asset Value) and Position Excel files with intelligent data processing and cleaning capabilities.

## 🚀 Features

- **Multi-format Support**: Handles both NAV and Position Excel files (.xlsx, .xls)
- **Intelligent File Detection**: Automatically detects file type based on filename and content
- **Comprehensive Validation**: 
  - File format and structure validation
  - Schema validation against predefined rules
  - Business logic data validation
- **Data Cleaning**: Automatically removes invalid rows and duplicates
- **Interactive Web Interface**: User-friendly Streamlit-based frontend
- **Complete Audit Trail**: Full logging and status tracking
- **LLM Integration**: Uses OpenAI GPT for intelligent validation feedback

## 📋 Prerequisites

- Python 3.8 or higher
- OpenAI API key (for LLM features)

## 🛠️ Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up environment variables**:
   - Copy `.env.template` to `.env`
   - Add your OpenAI API key:
     ```
     OPENAI_API_KEY=your_api_key_here
     ```

3. **Run integration test** (optional but recommended):
   ```bash
   python test_integration.py
   ```

## 🚀 Quick Start

### Option 1: Using the startup script
```bash
python run_app.py
```

### Option 2: Direct Streamlit command
```bash
streamlit run frontend/main_launcher.py
```

The application will open in your default web browser at `http://localhost:8501`

## 📁 Project Structure

```
CBRE_Validation_Agent/
├── agent/                      # Core validation agents
│   ├── file_validation.py      # File format validation
│   ├── schema_validation.py    # Schema validation
│   ├── data_validation.py      # Business logic validation
│   ├── file_creation.py        # File generation and cleaning
│   └── file_upload.py          # File upload handling
├── core/                       # Core system components
│   ├── master_agent.py         # Main orchestration agent
│   ├── llm_config.py          # LLM configuration
│   └── config_loader.py       # Configuration management
├── frontend/                   # Streamlit web interface
│   ├── pages/                  # Application pages
│   ├── components/             # Reusable UI components
│   └── utils/                  # Frontend utilities
├── validation_rules/           # Validation schemas
│   ├── nav_schema.json         # NAV file schema
│   ├── position_schema.json    # Position file schema
│   └── shared_rules.py         # Common validation rules
├── config/                     # Configuration files
├── data/                       # Data directories
└── test_integration.py         # Integration test suite
```

## 🔄 Workflow

1. **Upload File**: Upload your NAV or Position Excel file
2. **Validation**: System runs comprehensive validation checks
3. **File Generation**: Creates cleaned version with invalid data removed
4. **Final Upload**: Saves processed file to final destination
5. **Audit Trail**: View complete log of all operations

## 📊 Supported File Types

### NAV Files
Required columns:
- UNIQUE_ID, PORTFOLIO_ID, REGISTERED_HOLDER
- NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED
- NO_OF_SHARES, COMMITTED_CAPITAL, PERIOD, FUND_NAME

### Position Files
Required columns:
- UNIQUE_ID, PORTFOLIO_ID, NO_OF_SHARES
- NAV, FUND_NAME

## 🧹 Data Cleaning Rules

The system automatically applies these cleaning rules:
- Removes rows with UNIQUE_ID starting with 'TOTAL'
- Removes rows with all financial fields as zero
- Removes rows with null/blank UNIQUE_ID
- Removes duplicate UNIQUE_ID entries (keeps first occurrence)
- Validates data types and business rules

## ⚙️ Configuration

### Environment Variables (.env)
```
OPENAI_API_KEY=your_key_here
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.3
MAX_FILE_SIZE_MB=50
```

### File Paths (config/paths.yaml)
Configure directories for uploads, generated files, and outputs.

### Metadata (config/metadata_config.yaml)
Configure file type detection keywords and validation settings.

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
2. **LLM Errors**: Check your OpenAI API key in .env file
3. **File Upload Issues**: Ensure data directories exist (they're created automatically)
4. **Schema Validation Failures**: Check if your file matches the expected column names

### Running Tests
```bash
python test_integration.py
```

### Debug Mode
Enable debug information in the Logs page to see detailed session state.

## 📝 Logs and Monitoring

- All operations are logged with timestamps
- Complete audit trail available in the Logs page
- Download logs as text file for external analysis
- Debug information available for troubleshooting

## 🤝 Support

If you encounter any issues:
1. Check the Logs page for detailed error information
2. Run the integration test to verify system health
3. Ensure all configuration files are properly set up
4. Verify your OpenAI API key is valid and has sufficient credits

## 📄 License

This project is proprietary software for CBRE validation processes.
