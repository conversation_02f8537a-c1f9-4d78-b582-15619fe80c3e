"""
Pandera Validator Utility

Utility class for creating and applying Pandera schemas for data validation.
Converts rules.json and column_template.json into Pandera validation schemas.
"""

import pandas as pd
import pandera as pa
from pandera import Column, DataFrameSchema, Check
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from datetime import datetime


class PanderaValidator:
    """Utility class for Pandera-based data validation"""
    
    def __init__(self):
        self.data_type_mapping = {
            "string": pa.String,
            "integer": pa.Int64,
            "float": pa.Float64,
            "decimal": pa.Float64,
            "boolean": pa.Bool,
            "date": pa.DateTime,
            "timestamp": pa.DateTime,
            "time": pa.String  # Handle as string for now
        }
    
    def create_schema_from_rules(self, rules: Dict[str, Any], schema: Dict[str, Any]) -> DataFrameSchema:
        """
        Create Pandera DataFrameSchema from rules and schema definitions
        
        Args:
            rules: Validation rules from rules.json
            schema: Column schema from column_template.json
            
        Returns:
            Pandera DataFrameSchema object
        """
        columns = {}
        
        # Combine rules and schema information
        all_columns = set(rules.keys()) | set(schema.keys())
        
        for column_name in all_columns:
            column_rule = rules.get(column_name, {})
            column_schema = schema.get(column_name, {})
            
            # Determine data type
            data_type = self._get_pandera_dtype(
                column_rule.get("data_type") or column_schema.get("data_type", "string")
            )
            
            # Determine nullability
            nullable = not column_rule.get("required", True) or column_schema.get("nullable", False)
            
            # Create checks from constraints
            checks = self._create_checks_from_constraints(
                column_rule.get("constraints", {}),
                column_rule.get("business_rules", [])
            )
            
            # Create column definition
            columns[column_name] = Column(
                dtype=data_type,
                nullable=nullable,
                checks=checks,
                name=column_name
            )
        
        return DataFrameSchema(columns=columns, strict=False)
    
    def _get_pandera_dtype(self, data_type: str):
        """Map data type string to Pandera dtype"""
        return self.data_type_mapping.get(data_type.lower(), pa.String)
    
    def _create_checks_from_constraints(self, constraints: Dict[str, Any], 
                                      business_rules: List[str]) -> List[Check]:
        """Create Pandera checks from constraints and business rules"""
        checks = []
        
        try:
            # Handle different constraint types
            for constraint_type, constraint_value in constraints.items():
                check = self._create_check_from_constraint(constraint_type, constraint_value)
                if check:
                    checks.append(check)
            
            # Handle business rules
            for rule in business_rules:
                check = self._create_check_from_business_rule(rule)
                if check:
                    checks.append(check)
                    
        except Exception as e:
            print(f"Warning: Failed to create checks from constraints: {str(e)}")
        
        return checks
    
    def _create_check_from_constraint(self, constraint_type: str, constraint_value: Any) -> Optional[Check]:
        """Create a Pandera check from a specific constraint"""
        try:
            if constraint_type == "min_value":
                return Check.greater_than_or_equal_to(constraint_value)
            elif constraint_type == "max_value":
                return Check.less_than_or_equal_to(constraint_value)
            elif constraint_type == "min_length":
                return Check(lambda x: x.astype(str).str.len() >= constraint_value, 
                           error=f"String length must be >= {constraint_value}")
            elif constraint_type == "max_length":
                return Check(lambda x: x.astype(str).str.len() <= constraint_value,
                           error=f"String length must be <= {constraint_value}")
            elif constraint_type == "pattern":
                return Check.str_matches(constraint_value)
            elif constraint_type == "allowed_values":
                return Check.isin(constraint_value)
            elif constraint_type == "unique":
                if constraint_value:
                    return Check(lambda x: ~x.duplicated().any(), 
                               error="Values must be unique")
            elif constraint_type == "not_null":
                if constraint_value:
                    return Check(lambda x: ~x.isnull().any(),
                               error="Values cannot be null")
                    
        except Exception as e:
            print(f"Warning: Failed to create check for {constraint_type}: {str(e)}")
        
        return None
    
    def _create_check_from_business_rule(self, rule: str) -> Optional[Check]:
        """Create a Pandera check from a business rule string"""
        try:
            # Simple pattern matching for common business rules
            rule_lower = rule.lower()
            
            if "positive" in rule_lower:
                return Check.greater_than(0, error="Value must be positive")
            elif "non-negative" in rule_lower:
                return Check.greater_than_or_equal_to(0, error="Value must be non-negative")
            elif "percentage" in rule_lower:
                return Check(lambda x: (x >= 0) & (x <= 100), 
                           error="Percentage must be between 0 and 100")
            elif "email" in rule_lower:
                return Check.str_matches(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                                       error="Invalid email format")
                
        except Exception as e:
            print(f"Warning: Failed to create check from business rule '{rule}': {str(e)}")
        
        return None
    
    def validate_dataframe(self, df: pd.DataFrame, schema: DataFrameSchema) -> Dict[str, Any]:
        """
        Validate DataFrame against Pandera schema
        
        Args:
            df: DataFrame to validate
            schema: Pandera DataFrameSchema
            
        Returns:
            Dictionary with validation results
        """
        try:
            # Attempt validation
            validated_df = schema.validate(df, lazy=True)
            
            # If we get here, validation passed
            return {
                "overall_valid": True,
                "valid_rows": len(df),
                "invalid_rows": 0,
                "errors": [],
                "warnings": [],
                "summary": {
                    "total_rows": len(df),
                    "total_columns": len(df.columns),
                    "validation_passed": True
                },
                "row_errors": {}
            }
            
        except pa.errors.SchemaErrors as e:
            # Handle multiple validation errors
            return self._process_schema_errors(df, e)
        
        except Exception as e:
            # Handle unexpected errors
            return {
                "overall_valid": False,
                "valid_rows": 0,
                "invalid_rows": len(df),
                "errors": [f"Validation error: {str(e)}"],
                "warnings": [],
                "summary": {
                    "total_rows": len(df),
                    "total_columns": len(df.columns),
                    "validation_passed": False
                },
                "row_errors": {}
            }
    
    def _process_schema_errors(self, df: pd.DataFrame, schema_errors: pa.errors.SchemaErrors) -> Dict[str, Any]:
        """Process Pandera schema errors into structured format"""
        row_errors = {}
        all_errors = []
        warnings = []
        
        try:
            # Process each error
            for error in schema_errors.schema_errors:
                error_info = {
                    "column": getattr(error, 'column', 'unknown'),
                    "message": str(error),
                    "error_type": type(error).__name__
                }
                
                # Try to extract row indices if available
                if hasattr(error, 'failure_cases') and error.failure_cases is not None:
                    failure_cases = error.failure_cases
                    if hasattr(failure_cases, 'index'):
                        for idx in failure_cases.index:
                            if idx not in row_errors:
                                row_errors[idx] = []
                            row_errors[idx].append(error_info)
                
                all_errors.append(error_info)
            
            # Calculate statistics
            invalid_rows = len(row_errors)
            valid_rows = len(df) - invalid_rows
            
            return {
                "overall_valid": False,
                "valid_rows": valid_rows,
                "invalid_rows": invalid_rows,
                "errors": [error['message'] for error in all_errors],
                "warnings": warnings,
                "summary": {
                    "total_rows": len(df),
                    "total_columns": len(df.columns),
                    "validation_passed": False,
                    "error_count": len(all_errors),
                    "affected_rows": invalid_rows
                },
                "row_errors": row_errors
            }
            
        except Exception as e:
            return {
                "overall_valid": False,
                "valid_rows": 0,
                "invalid_rows": len(df),
                "errors": [f"Error processing validation results: {str(e)}"],
                "warnings": [],
                "summary": {
                    "total_rows": len(df),
                    "total_columns": len(df.columns),
                    "validation_passed": False
                },
                "row_errors": {}
            }
    
    def create_sample_schema(self) -> DataFrameSchema:
        """Create a sample schema for testing purposes"""
        return DataFrameSchema({
            "id": Column(pa.Int64, nullable=False, checks=[Check.greater_than(0)]),
            "name": Column(pa.String, nullable=False, checks=[Check.str_length(min_value=1)]),
            "email": Column(pa.String, nullable=True, checks=[
                Check.str_matches(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
            ]),
            "age": Column(pa.Int64, nullable=True, checks=[
                Check.greater_than_or_equal_to(0),
                Check.less_than_or_equal_to(150)
            ]),
            "salary": Column(pa.Float64, nullable=True, checks=[Check.greater_than(0)])
        })
    
    def get_schema_info(self, schema: DataFrameSchema) -> Dict[str, Any]:
        """Get information about a Pandera schema"""
        return {
            "columns": list(schema.columns.keys()),
            "column_count": len(schema.columns),
            "strict_mode": schema.strict,
            "column_details": {
                name: {
                    "dtype": str(col.dtype),
                    "nullable": col.nullable,
                    "checks_count": len(col.checks) if col.checks else 0
                }
                for name, col in schema.columns.items()
            }
        }
