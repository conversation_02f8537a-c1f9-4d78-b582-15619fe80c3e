"""
AI Agent Base Class for CBRE Validation System
Provides foundation for all AI-powered validation agents
"""

import json
from datetime import datetime

class AIAgentBase:
    """Base class for all AI agents in the CBRE validation system"""

    def __init__(self, agent_name: str, agent_role: str):
        self.agent_name = agent_name
        self.agent_role = agent_role

        # Initialize LLM
        try:
            from core.llm_config import get_llm
            self.llm = get_llm()
            self.ai_available = True
        except Exception as e:
            print(f"Warning: AI not available - {str(e)}")
            self.llm = None
            self.ai_available = False

        self.conversation_history = []
        self.analysis_results = []
        
    def log_interaction(self, prompt: str, response: str, metadata=None):
        """Log AI agent interactions for detailed reporting"""
        interaction = {
            "timestamp": datetime.now().isoformat(),
            "agent": self.agent_name,
            "prompt": prompt,
            "response": response,
            "metadata": metadata or {}
        }
        self.conversation_history.append(interaction)
        
    def analyze_with_ai(self, system_prompt: str, user_prompt: str, context_data=None):
        """Core AI analysis method with fallback handling"""
        try:
            # Check if AI is available
            if not self.ai_available or self.llm is None:
                return {
                    "success": False,
                    "error": "AI dependencies not available",
                    "raw_response": "AI analysis unavailable. Install: pip install langchain langchain-openai openai",
                    "structured_response": {
                        "analysis": "AI dependencies not installed",
                        "recommendation": "Install dependencies with: pip install langchain langchain-openai openai"
                    },
                    "agent": self.agent_name,
                    "timestamp": datetime.now().isoformat()
                }

            # Prepare messages
            from langchain.schema import SystemMessage, HumanMessage

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            # Get AI response
            response = self.llm(messages)
            ai_response = response.content
            
            # Log the interaction
            self.log_interaction(
                prompt=user_prompt,
                response=ai_response,
                metadata={
                    "system_prompt": system_prompt,
                    "context_data": context_data,
                    "model": self.llm.model_name if hasattr(self.llm, 'model_name') else "unknown"
                }
            )
            
            # Try to parse structured response if it looks like JSON
            structured_response = self._try_parse_json_response(ai_response)
            
            return {
                "success": True,
                "raw_response": ai_response,
                "structured_response": structured_response,
                "agent": self.agent_name,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            error_msg = f"AI analysis failed: {str(e)}"
            self.log_interaction(
                prompt=user_prompt,
                response=error_msg,
                metadata={"error": True, "exception": str(e)}
            )
            
            return {
                "success": False,
                "error": error_msg,
                "agent": self.agent_name,
                "timestamp": datetime.now().isoformat()
            }
    
    def _try_parse_json_response(self, response: str):
        """Try to extract JSON from AI response"""
        try:
            # Look for JSON blocks in the response
            import re
            json_pattern = r'```json\s*(.*?)\s*```'
            json_match = re.search(json_pattern, response, re.DOTALL)
            
            if json_match:
                return json.loads(json_match.group(1))
            
            # Try to parse the entire response as JSON
            return json.loads(response)
            
        except (json.JSONDecodeError, AttributeError):
            return None
    
    def generate_detailed_report(self):
        """Generate comprehensive AI agent report"""
        return {
            "agent_name": self.agent_name,
            "agent_role": self.agent_role,
            "total_interactions": len(self.conversation_history),
            "analysis_summary": self._summarize_analysis(),
            "conversation_history": self.conversation_history,
            "recommendations": self._generate_recommendations(),
            "timestamp": datetime.now().isoformat()
        }
    
    def _summarize_analysis(self) -> str:
        """AI-powered summary of all analysis performed"""
        if not self.conversation_history:
            return "No analysis performed"
        
        try:
            # Create summary prompt
            interactions_summary = "\n".join([
                f"- {interaction['prompt'][:100]}... -> {interaction['response'][:100]}..."
                for interaction in self.conversation_history[-5:]  # Last 5 interactions
            ])
            
            summary_prompt = f"""
            As the {self.agent_name}, provide a concise summary of the analysis work performed:
            
            Recent interactions:
            {interactions_summary}
            
            Provide a 2-3 sentence summary of what was analyzed and the key findings.
            """
            
            result = self.analyze_with_ai(
                system_prompt=f"You are {self.agent_name} providing a summary of your analysis work.",
                user_prompt=summary_prompt
            )
            
            return result.get("raw_response", "Summary generation failed")
            
        except Exception as e:
            return f"Summary generation error: {str(e)}"
    
    def _generate_recommendations(self):
        """AI-powered recommendations based on analysis"""
        if not self.conversation_history:
            return ["No recommendations available - no analysis performed"]
        
        try:
            # Extract key issues from conversation history
            issues = []
            for interaction in self.conversation_history:
                if "error" in interaction.get("metadata", {}) or "fail" in interaction["response"].lower():
                    issues.append(interaction["response"][:200])
            
            if not issues:
                return ["No critical issues identified"]
            
            rec_prompt = f"""
            Based on the issues identified during analysis:
            {chr(10).join(f"- {issue}" for issue in issues[:3])}
            
            Provide 3-5 specific, actionable recommendations to address these issues.
            Format as a simple list.
            """
            
            result = self.analyze_with_ai(
                system_prompt=f"You are {self.agent_name} providing actionable recommendations.",
                user_prompt=rec_prompt
            )
            
            response = result.get("raw_response", "")
            # Extract recommendations from response
            recommendations = [line.strip("- ").strip() for line in response.split("\n") if line.strip().startswith("-")]
            
            return recommendations if recommendations else [response]
            
        except Exception as e:
            return [f"Recommendation generation error: {str(e)}"]
    
    def reset_conversation(self):
        """Reset conversation history for new analysis"""
        self.conversation_history = []
        self.analysis_results = []
