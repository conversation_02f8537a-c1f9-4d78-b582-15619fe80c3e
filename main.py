#!/usr/bin/env python3
"""
CBRE Validation Agent - Main Pipeline Entry Point

This is the main entry point for running the complete validation pipeline.
Orchestrates the four-agent flow: SP_Parser → FileValidation → DataValidation → Guardrail

Usage:
    python main.py --sql-file path/to/rules.sql --data-file path/to/data.xlsx
    python main.py --config config/pipeline_config.yaml
    python main.py --interactive  # Interactive mode
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional

from core.pipeline_orchestrator import PipelineOrchestrator
from core.config_manager import Config<PERSON>anager


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="CBRE Validation Agent - AI-powered file validation pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --sql-file data/input/validation_rules.sql --data-file data/input/sample_data.xlsx
  python main.py --config config/pipeline_config.yaml
  python main.py --interactive
  python main.py --demo  # Run with sample files
        """
    )
    
    parser.add_argument(
        "--sql-file", "-s",
        type=str,
        help="Path to SQL stored procedure file containing validation rules"
    )
    
    parser.add_argument(
        "--data-file", "-d", 
        type=str,
        help="Path to Excel data file to validate"
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        help="Path to pipeline configuration YAML file"
    )
    
    parser.add_argument(
        "--output-dir", "-o",
        type=str,
        default="data/output",
        help="Output directory for generated files (default: data/output)"
    )
    
    parser.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="Run in interactive mode"
    )
    
    parser.add_argument(
        "--demo",
        action="store_true", 
        help="Run demo with sample files"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    parser.add_argument(
        "--report",
        action="store_true",
        help="Generate detailed pipeline report"
    )
    
    return parser.parse_args()


def setup_logging(verbose: bool = False):
    """Setup logging configuration"""
    import logging
    
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('data/logs/main_pipeline.log')
        ]
    )


def load_configuration(config_path: Optional[str] = None) -> Dict[str, Any]:
    """Load pipeline configuration"""
    try:
        config_manager = ConfigManager()
        
        if config_path:
            return config_manager.load_config(config_path)
        else:
            # Load default configuration
            return config_manager.get_default_config()
            
    except Exception as e:
        print(f"Warning: Failed to load configuration: {str(e)}")
        return {}


def run_interactive_mode():
    """Run pipeline in interactive mode"""
    print("\n🤖 CBRE Validation Agent - Interactive Mode")
    print("=" * 50)
    
    # Get SQL file
    while True:
        sql_file = input("\nEnter path to SQL stored procedure file: ").strip()
        if not sql_file:
            print("SQL file path is required.")
            continue
        if not Path(sql_file).exists():
            print(f"File not found: {sql_file}")
            continue
        break
    
    # Get data file
    while True:
        data_file = input("Enter path to Excel data file: ").strip()
        if not data_file:
            print("Data file path is required.")
            continue
        if not Path(data_file).exists():
            print(f"File not found: {data_file}")
            continue
        break
    
    # Get output directory
    output_dir = input("Enter output directory (default: data/output): ").strip()
    if not output_dir:
        output_dir = "data/output"
    
    return sql_file, data_file, output_dir


def run_demo_mode():
    """Run pipeline with demo/sample files"""
    print("\n🎯 Running Demo Mode with Sample Files")
    print("=" * 40)
    
    # Create sample files if they don't exist
    create_sample_files()
    
    sql_file = "data/samples/sample_validation_rules.sql"
    data_file = "data/samples/sample_data.xlsx"
    output_dir = "data/output"
    
    return sql_file, data_file, output_dir


def create_sample_files():
    """Create sample files for demo mode"""
    samples_dir = Path("data/samples")
    samples_dir.mkdir(parents=True, exist_ok=True)
    
    # Create sample SQL file
    sample_sql = """
-- Sample validation rules for demo
CREATE TABLE sample_data (
    id INTEGER NOT NULL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    age INTEGER CHECK (age >= 0 AND age <= 150),
    salary DECIMAL(10,2) CHECK (salary > 0),
    department VARCHAR(50) NOT NULL,
    hire_date DATE NOT NULL
);

-- Additional validation rules
ALTER TABLE sample_data ADD CONSTRAINT unique_email UNIQUE (email);
ALTER TABLE sample_data ADD CONSTRAINT valid_department 
    CHECK (department IN ('Engineering', 'Sales', 'Marketing', 'HR', 'Finance'));
"""
    
    sql_file = samples_dir / "sample_validation_rules.sql"
    with open(sql_file, 'w') as f:
        f.write(sample_sql)
    
    # Create sample Excel data
    import pandas as pd
    
    sample_data = {
        'id': [1, 2, 3, 4, 5],
        'name': ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Brown', 'Charlie Wilson'],
        'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'age': [30, 25, 45, 35, 28],
        'salary': [75000.00, 65000.00, 95000.00, 80000.00, 70000.00],
        'department': ['Engineering', 'Sales', 'Engineering', 'Marketing', 'HR'],
        'hire_date': ['2020-01-15', '2021-03-10', '2019-07-22', '2020-11-05', '2021-09-18']
    }
    
    df = pd.DataFrame(sample_data)
    excel_file = samples_dir / "sample_data.xlsx"
    df.to_excel(excel_file, index=False)
    
    print(f"✅ Sample files created:")
    print(f"   SQL: {sql_file}")
    print(f"   Excel: {excel_file}")


def display_results(result, verbose: bool = False):
    """Display pipeline results"""
    print("\n📊 Pipeline Results")
    print("=" * 30)
    
    if result.success:
        print("✅ Pipeline completed successfully!")
        
        if result.data:
            summary = result.data.get("pipeline_summary", {})
            print(f"\n📈 Summary:")
            print(f"   Total stages: {summary.get('total_stages', 0)}")
            print(f"   Successful stages: {summary.get('successful_stages', 0)}")
            print(f"   Failed stages: {summary.get('failed_stages', 0)}")
            
            final_outputs = result.data.get("final_outputs", {})
            if final_outputs:
                print(f"\n📁 Generated Files:")
                for output_type, file_path in final_outputs.items():
                    if file_path:
                        print(f"   {output_type}: {file_path}")
    else:
        print("❌ Pipeline failed!")
        
        if result.errors:
            print(f"\n🚨 Errors:")
            for error in result.errors:
                print(f"   • {error}")
    
    if result.warnings:
        print(f"\n⚠️  Warnings:")
        for warning in result.warnings:
            print(f"   • {warning}")
    
    if verbose and result.metadata:
        print(f"\n🔍 Detailed Information:")
        print(json.dumps(result.metadata, indent=2))


def main():
    """Main entry point"""
    args = parse_arguments()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Load configuration
    config = load_configuration(args.config)
    if args.output_dir:
        config["output_dir"] = args.output_dir
    
    print("🏢 CBRE Validation Agent")
    print("AI-Powered File Validation Pipeline")
    print("=" * 40)
    
    try:
        # Determine input files
        if args.interactive:
            sql_file, data_file, output_dir = run_interactive_mode()
            config["output_dir"] = output_dir
        elif args.demo:
            sql_file, data_file, output_dir = run_demo_mode()
            config["output_dir"] = output_dir
        elif args.sql_file and args.data_file:
            sql_file = args.sql_file
            data_file = args.data_file
        else:
            print("❌ Error: Must specify either --sql-file and --data-file, --config, --interactive, or --demo")
            sys.exit(1)
        
        # Validate input files exist
        if not Path(sql_file).exists():
            print(f"❌ Error: SQL file not found: {sql_file}")
            sys.exit(1)
        
        if not Path(data_file).exists():
            print(f"❌ Error: Data file not found: {data_file}")
            sys.exit(1)
        
        print(f"\n📂 Input Files:")
        print(f"   SQL Rules: {sql_file}")
        print(f"   Data File: {data_file}")
        print(f"   Output Dir: {config.get('output_dir', 'data/output')}")
        
        # Initialize and run pipeline
        print(f"\n🚀 Starting Pipeline...")
        orchestrator = PipelineOrchestrator(config)
        result = orchestrator.run(sql_file, data_file)
        
        # Display results
        display_results(result, args.verbose)
        
        # Generate report if requested
        if args.report:
            report_path = orchestrator.save_pipeline_report()
            print(f"\n📋 Detailed report saved: {report_path}")
        
        # Exit with appropriate code
        sys.exit(0 if result.success else 1)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
