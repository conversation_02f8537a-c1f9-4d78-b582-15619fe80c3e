"""
FileValidationAgent - AI Rule Enforcer Agent

Validates uploaded files against rules and schema using Pandera.
Flags invalid rows and generates comprehensive diagnostics.

Outputs:
- validated_output.xlsx: Original data with error and error_message columns
"""

import json
import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

from core.agent_base import AgentBase, AgentResponse
from .pandera_validator import PanderaValidator


class FileValidationAgent(AgentBase):
    """
    AI Rule Enforcer Agent for File Validation
    
    Responsibilities:
    1. Validate uploaded .xlsx files against rules.json and column_template.json
    2. Apply constraints using Pandera validation framework
    3. Flag invalid rows and generate detailed diagnostics
    4. Output validated_output.xlsx with error annotations
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            agent_name="FileValidationAgent",
            agent_type="AI Rule Enforcer Agent", 
            agent_role="File validation and rule enforcement specialist"
        )
        
        self.config = config or {}
        self.validator = PanderaValidator()
        self.output_dir = Path(self.config.get("output_dir", "data/output"))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def run(self, file_path: str, rules_path: str, schema_path: str) -> AgentResponse:
        """
        Main execution method for file validation
        
        Args:
            file_path: Path to the Excel file to validate
            rules_path: Path to rules.json file
            schema_path: Path to column_template.json file
            
        Returns:
            AgentResponse with validation results
        """
        try:
            self.log_info(f"Starting file validation: {file_path}")
            
            # Load input data
            df = self._load_excel_file(file_path)
            rules = self._load_rules(rules_path)
            schema = self._load_schema(schema_path)
            
            # Validate file structure
            structure_valid, structure_errors = self._validate_file_structure(df, schema)
            
            # Validate data against rules
            validation_results = self._validate_data_with_pandera(df, rules, schema)
            
            # Generate output file with error annotations
            output_path = self._generate_validated_output(
                df, validation_results, file_path
            )
            
            # Compile results
            success = structure_valid and validation_results.get("overall_valid", False)
            
            response_data = {
                "input_file": file_path,
                "output_file": str(output_path),
                "total_rows": len(df),
                "valid_rows": validation_results.get("valid_rows", 0),
                "invalid_rows": validation_results.get("invalid_rows", 0),
                "structure_valid": structure_valid,
                "validation_summary": validation_results.get("summary", {}),
                "error_details": validation_results.get("errors", [])
            }
            
            errors = structure_errors + validation_results.get("errors", [])
            
            if success:
                self.log_success(f"File validation completed successfully. {response_data['valid_rows']}/{response_data['total_rows']} rows valid")
            else:
                self.log_warning(f"File validation completed with issues. {len(errors)} errors found")
            
            return self.create_response(
                success=success,
                data=response_data,
                errors=errors,
                warnings=validation_results.get("warnings", [])
            )
            
        except Exception as e:
            error_msg = f"File validation failed: {str(e)}"
            self.log_error(error_msg)
            return self.create_response(
                success=False,
                errors=[error_msg]
            )
    
    def _load_excel_file(self, file_path: str) -> pd.DataFrame:
        """Load Excel file into DataFrame"""
        try:
            df = pd.read_excel(file_path)
            self.log_info(f"Loaded Excel file with {len(df)} rows and {len(df.columns)} columns")
            return df
        except Exception as e:
            raise Exception(f"Failed to load Excel file: {str(e)}")
    
    def _load_rules(self, rules_path: str) -> Dict[str, Any]:
        """Load validation rules from JSON file"""
        try:
            with open(rules_path, 'r', encoding='utf-8') as f:
                rules_data = json.load(f)
            
            rules = rules_data.get("rules", {})
            self.log_info(f"Loaded {len(rules)} validation rules")
            return rules
        except Exception as e:
            raise Exception(f"Failed to load rules file: {str(e)}")
    
    def _load_schema(self, schema_path: str) -> Dict[str, Any]:
        """Load column schema from JSON file"""
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema_data = json.load(f)
            
            schema = schema_data.get("schema", {})
            self.log_info(f"Loaded schema for {len(schema)} columns")
            return schema
        except Exception as e:
            raise Exception(f"Failed to load schema file: {str(e)}")
    
    def _validate_file_structure(self, df: pd.DataFrame, schema: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate file structure against schema"""
        errors = []
        
        try:
            # Check required columns
            required_columns = [col for col, info in schema.items() 
                              if not info.get("nullable", True)]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                errors.append(f"Missing required columns: {missing_columns}")
            
            # Check for unexpected columns
            expected_columns = set(schema.keys())
            actual_columns = set(df.columns)
            unexpected_columns = actual_columns - expected_columns
            
            if unexpected_columns:
                errors.append(f"Unexpected columns found: {list(unexpected_columns)}")
            
            # Check for empty file
            if len(df) == 0:
                errors.append("File is empty")
            
            structure_valid = len(errors) == 0
            
            if structure_valid:
                self.log_info("File structure validation passed")
            else:
                self.log_warning(f"File structure validation failed: {errors}")
            
            return structure_valid, errors
            
        except Exception as e:
            error_msg = f"Structure validation error: {str(e)}"
            self.log_error(error_msg)
            return False, [error_msg]

    def _validate_data_with_pandera(self, df: pd.DataFrame, rules: Dict[str, Any],
                                   schema: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data using Pandera framework"""
        try:
            # Create Pandera schema from rules and schema
            pandera_schema = self.validator.create_schema_from_rules(rules, schema)

            # Validate DataFrame
            validation_result = self.validator.validate_dataframe(df, pandera_schema)

            self.log_info(f"Pandera validation completed. Valid rows: {validation_result.get('valid_rows', 0)}")

            return validation_result

        except Exception as e:
            error_msg = f"Pandera validation error: {str(e)}"
            self.log_error(error_msg)
            return {
                "overall_valid": False,
                "valid_rows": 0,
                "invalid_rows": len(df),
                "errors": [error_msg],
                "warnings": [],
                "summary": {}
            }

    def _generate_validated_output(self, df: pd.DataFrame, validation_results: Dict[str, Any],
                                 input_file_path: str) -> Path:
        """Generate validated output Excel file with error annotations"""
        try:
            # Create a copy of the original DataFrame
            output_df = df.copy()

            # Add error columns
            output_df['validation_status'] = 'VALID'
            output_df['error_message'] = ''
            output_df['error_details'] = ''

            # Apply validation results
            if 'row_errors' in validation_results:
                for row_idx, errors in validation_results['row_errors'].items():
                    if errors:
                        output_df.loc[row_idx, 'validation_status'] = 'INVALID'
                        output_df.loc[row_idx, 'error_message'] = '; '.join([e['message'] for e in errors])
                        output_df.loc[row_idx, 'error_details'] = json.dumps(errors)

            # Generate output filename
            input_name = Path(input_file_path).stem
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"{input_name}_validated_{timestamp}.xlsx"
            output_path = self.output_dir / output_filename

            # Save to Excel
            output_df.to_excel(output_path, index=False)

            self.log_success(f"Generated validated output file: {output_path}")
            return output_path

        except Exception as e:
            error_msg = f"Failed to generate validated output: {str(e)}"
            self.log_error(error_msg)
            raise Exception(error_msg)

    def validate_file_format(self, file_path: str) -> Tuple[bool, List[str]]:
        """Validate file format and basic structure"""
        errors = []

        try:
            # Check file extension
            if not file_path.lower().endswith(('.xlsx', '.xls')):
                errors.append("File must be an Excel file (.xlsx or .xls)")

            # Check file exists
            if not Path(file_path).exists():
                errors.append("File does not exist")

            # Check file size
            file_size = Path(file_path).stat().st_size
            max_size = self.config.get("max_file_size_mb", 100) * 1024 * 1024
            if file_size > max_size:
                errors.append(f"File size exceeds maximum allowed size ({max_size / 1024 / 1024:.1f} MB)")

            # Try to read the file
            try:
                pd.read_excel(file_path, nrows=1)
            except Exception as e:
                errors.append(f"Cannot read Excel file: {str(e)}")

            return len(errors) == 0, errors

        except Exception as e:
            return False, [f"File format validation error: {str(e)}"]

    def get_validation_summary(self) -> Dict[str, Any]:
        """Get summary of validation operations"""
        return {
            "agent_name": self.agent_name,
            "agent_type": self.agent_type,
            "operations_performed": len(self.operation_history),
            "last_validation": self.operation_history[-1] if self.operation_history else None,
            "configuration": self.config
        }
